#!/bin/bash

if [[ "$1" == "list-unit-files" && "$2" == "--type=service" ]]; then
    # systemctl list-unit-files --type=service
    echo "xstor_tgt_3260.service"
elif [[ "$1" == "is-active" && "$2" == "xstor_tgt_3260" ]]; then
    # systemctl is-active xstor_tgt_3260
    # here we only check sock file is enough
    if [ -e "/var/tmp/xstor_tgt_3260.sock" ]; then
        echo "active"
    else
        echo "inactive"
    fi
elif [[ "$1" == "is-active" && "$2" == "xstor-targetgw" ]]; then
    # systemctl is-active xstor-targetgw
    echo "active"
else
    echo "Command not supported in container"
    exit 1
fi