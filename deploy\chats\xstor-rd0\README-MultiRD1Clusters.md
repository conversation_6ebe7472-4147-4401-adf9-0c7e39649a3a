# 多RD1集群配置说明

## 概述

此功能允许 xstor-rd0 组件对接多个 RD1 集群。当配置了多RD1集群变量时，start-tapi.sh 脚本会将这些变量渲染到配置文件中；如果变量为空，则保持原来的单集群逻辑。

## 配置方式

### 1. 单集群配置（默认）

```yaml
config:
  backendType: "light"
  mgrAddr: "*************:5000"
  multiRD1Clusters: {}
```

在这种情况下，系统会使用原有的单集群逻辑，配置文件中只包含：
- `cluster_name = light`
- `master_ip_group = *************`

### 2. 多集群配置

```yaml
config:
  backendType: "light"
  mgrAddr: ""  # 多集群模式下可以为空
  multiRD1Clusters:
    multi_clusters: "cluster1,cluster2,cluster3"
    master_ip_group_cluster1: "*************"
    master_ip_group_cluster2: "*************"
    master_ip_group_cluster3: "*************"
```

在这种情况下，系统会生成包含多集群信息的配置文件：
- `cluster_name = light`
- `master_ip_group = *************,*************,*************`
- `multi_clusters = cluster1,cluster2,cluster3`
- `master_ip_group_cluster1 = *************`
- `master_ip_group_cluster2 = *************`
- `master_ip_group_cluster3 = *************`

## 环境变量传递

配置中的 `multiRD1Clusters` 字段会被转换为环境变量传递给 pod：

- `multi_clusters` → `MULTI_CLUSTERS`
- `master_ip_group_cluster1` → `MASTER_IP_GROUP_CLUSTER1`
- `master_ip_group_cluster2` → `MASTER_IP_GROUP_CLUSTER2`
- 等等...

## 脚本逻辑

start-tapi.sh 脚本的处理逻辑：

1. **检查 MULTI_CLUSTERS 环境变量**
   - 如果为空：使用单集群模式，从 MGRAddr 获取 master_ip_group
   - 如果不为空：使用多集群模式

2. **多集群模式处理**
   - 解析 MULTI_CLUSTERS 中的集群列表
   - 为每个集群查找对应的 MASTER_IP_GROUP_* 环境变量
   - 将所有集群的 IP 组合成 master_ip_group
   - 在配置文件中添加 multi_clusters 和各个集群的 master_ip_group_* 配置

3. **域名解析**
   - 支持域名到IP的解析（保持原有功能）
   - 对所有 IP 地址进行域名解析处理

## 部署示例

### 单集群部署
```bash
helm install xstor-rd0 . --set config.backendType=light --set config.mgrAddr="*************:5000"
```

### 多集群部署
```bash
helm install xstor-rd0 . \
  --set config.backendType=light \
  --set config.multiRD1Clusters.multi_clusters="cluster1,cluster2" \
  --set config.multiRD1Clusters.master_ip_group_cluster1="*************" \
  --set config.multiRD1Clusters.master_ip_group_cluster2="*************"
```

## 注意事项

1. **向后兼容性**：如果不配置 multiRD1Clusters 或配置为空对象 `{}`，系统会保持原有的单集群行为
2. **环境变量命名**：集群名称会被转换为大写，并添加 `MASTER_IP_GROUP_` 前缀
3. **IP地址格式**：支持 IP 地址和域名，脚本会自动进行域名解析
4. **错误处理**：如果配置了多集群但某个集群的 IP 地址缺失，会记录警告日志但不会中断启动
