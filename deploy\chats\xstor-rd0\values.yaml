# Default values for xstor-rd0.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.


config:
  #对接后端类型: rbd | light
  backendType: "rbd"
  transport: "tcp"
  protocol: "nvmf"
  xTGTPort: "14420"
  # rd0 targetapi端口
  tAPIPort: "5001"
  # poolName here for rbd backend type
  poolName: "rbd_nvme"
  tAPIAuth: "<user>/<Token>"
  # 轻量化需要配置MGR地址,如果对接多rd1集群需要配置 multiRD1Clusters
  mgrAddr: ""
  # 对接多rd1集群配置, 如果没有对接多集群可不配置，默认单rd1集群名称为light
  multiRD1Clusters: {}
  # multiRD1Clusters:
  #   multi_clusters: cluster1,cluster2
  #   master_ip_group_cluster1: *************
  #   master_ip_group_cluster2: *************

image:
  tAPI:
    repository: harbor.ctyun.store:23443/xstorecsi/xstor_rd0
    tag: v0.2.0
  xTGT:
    repository: harbor.ctyun.store:23443/xstorecsi/xstor_rd0
    tag: v0.2.0
  wsProxy:
    repository: harbor.ctyun.store:23443/xstorecsi/xstor_rd0
    tag: v0.2.0

# 选择节点拉起rd0 pod, 不配置则默认所有node节点
rd0Nodes:
# - R03-P05-K8STest-001.xvod.nm.cn
# - R03-P05-K8STest-002.xvod.nm.cn
