#!/bin/bash

set -e
set -x
export TZ=Asia/Shanghai
source /root/utils
TGTConfig=/root/spdk_tgt.conf
TGTMEMSize=10240
TGTCPUNum=8
TGTRPCSocket=/var/tmp/spdk.sock
XSTORTGTSocket=/var/tmp/xstor_tgt_3260.sock
TGTCPUMask=""
TGTConfigFromAPI=/opt/nvmf_target/nvmf.cfg
TGTConfigCompleted=${TGTConfigFromAPI}.completed


initialize_vars() {
    TGTCPUMask=$(get_cpumask ${TGTCPUNum})
}
restore_rbd_xtgt_config() {
    ensure_env_var APIAuth
    ensure_env_var TAPIPort
    ensure_env_var NodeHostIP
    HttpAPIAuth=$(echo ${APIAuth} | sed 's%/%:%g')
    log_info "Restoring spdk tgt configure..."
    while [ 1 ]; do
        if [ ! -f ${TGTConfigCompleted} ]; then
            log_exec curl --insecure --user ${HttpAPIAuth} -X PUT http://${NodeHostIP}:${TAPIPort}/api/saveconfig
        fi
        if [ $? -eq 0 ] && [ -f ${TGTConfigFromAPI} ]; then
            break
        fi
        log_info "Waiting for %{TGTConfigFromAPI} to be created..."
        sleep 1
    done
    if [ -f ${TGTConfigFromAPI} ]; then
        TGTConfig=${TGTConfig}.$(date +%s)
        # copy a new config to avoid file conflict
        cp ${TGTConfigFromAPI} ${TGTConfig}
        # remove the completed file, so that restart tgt only will curl to create a new config
        rm -rf ${TGTConfigCompleted}
    fi
    if [ $? -ne 0 ]; then
        log_error "restore_rbd_xtgt_config failed."
        exit -1
    fi
}
restore_xstor_xtgt_config() {
    ensure_env_var NodeHostIP
    ensure_env_var MGRAddr
    log_info "Restoring spdk tgt configure from mgr..."
    python3 /root/config_parser.py -r rd0 -v${MGRAddr} -a${NodeHostIP} -o ${TGTConfig}
    if [ $? -ne 0 ]; then
        log_error "restore_xstor_xtgt_config failed."
        exit -1
    fi
}
start_rbd_xtgt() {
    SPDK_TGT_BIN=/root/spdk_tgt
    cpuinfo=$(cat /proc/cpuinfo | grep 'vendor_id' | head -n 1 | awk '{print $3}')
    if [ "$cpuinfo" == "HygonGenuine" ]; then
        log_info "CPU vendor is hygon, use tgt of hygon instead."
        SPDK_TGT_BIN=/root/spdk_tgt_hygon
    fi
    if [ -f ${TGTConfig} ]; then
        log_exec ${SPDK_TGT_BIN} --no-huge --iova-mode va \
            -s ${TGTMEMSize} -m ${TGTCPUMask} \
            -r ${TGTRPCSocket} -c ${TGTConfig}
    else
        log_exec ${SPDK_TGT_BIN} --no-huge --iova-mode va \
            -s ${TGTMEMSize} -m ${TGTCPUMask} \
            -r ${TGTRPCSocket}
    fi
    if [ $? -ne 0 ]; then
        log_error "start_rbd_xtgt failed."
        exit -1
    fi
}
start_xstor_xtgt() {
    SPDK_TGT_BIN=/root/xstor_tgt
    cpuinfo=$(cat /proc/cpuinfo | grep 'vendor_id' | head -n 1 | awk '{print $3}')
    if [ "$cpuinfo" == "HygonGenuine" ]; then
        log_info "CPU vendor is hygon, use tgt of hygon instead."
        SPDK_TGT_BIN=/root/xstor_tgt_hygon
    fi
    if [ -f ${TGTConfig} ]; then
        log_exec ${SPDK_TGT_BIN} --no-huge --iova-mode va \
            -s ${TGTMEMSize} -m ${TGTCPUMask} \
            -r ${TGTRPCSocket} -c ${TGTConfig}
    else
        log_exec ${SPDK_TGT_BIN} --no-huge --iova-mode va \
            -s ${TGTMEMSize} -m ${TGTCPUMask} \
            -r ${TGTRPCSocket}
    fi
    if [ $? -ne 0 ]; then
        log_error "start_xstor_xtgt failed."
        exit -1
    fi
}
main() {
    initialize_vars
    if [ ! -z "$1" ] && [ "$1" == "rbd" ]; then
        log_info "Restoring rbd tgt config..."
        restore_rbd_xtgt_config
        start_rbd_xtgt
    elif [ "$1" == "light" ]; then
        log_info "Restoring xstor tgt config..."
        restore_xstor_xtgt_config
        TGTRPCSocket=${XSTORTGTSocket}
        start_xstor_xtgt
    fi
}
main "$@"
