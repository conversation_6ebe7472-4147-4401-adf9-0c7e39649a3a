#!/usr/bin/env python3
import json
import os
import sys
import argparse
import socket
import requests
from requests import Response
import copy
import datetime

g_clientiqn = 'iqn.2099-01.com.raid.c:'
g_targetiqn = 'iqn.2099-01.com.raid.t:'
g_targetnqn = 'nqn.2099-01.com.raid.t:'
g_clientnqn = 'nqn.2099-01.com.raid.c:'
tgt_dir = '/opt/xstor_tgt/bitmapfile/config-json/'
redirect_dir = '/opt/xstor_tgt/bitmapfile/redirect-config-json/'

global_config_seed =    {
                        "subsystems": [
                            {
                            "subsystem": "scheduler",
                            "config": [
                                {
                                "method": "framework_set_scheduler",
                                "params": {
                                    "name": "static",
                                    "period": 1000000
                                    }
                                }]
                            },
                            {
                            "subsystem": "accel",
                            "config": []
                            },
                            {
                            "subsystem": "vmd",
                            "config": []
                            },
                            {
                            "subsystem": "sock",
                            "config": [
                                {
                                "method": "sock_impl_set_options",
                                "params": {
                                    "impl_name": "posix",
                                    "recv_buf_size": 2097152,
                                    "send_buf_size": 2097152,
                                    "enable_recv_pipe": True,
                                    "enable_quickack": False,
                                    "enable_placement_id": 0,
                                    "enable_zerocopy_send_server": True,
                                    "enable_zerocopy_send_client": False,
                                    "zerocopy_threshold": 0
                                    }
                                }]
                            },
                            {
                            "subsystem": "bdev",
                            "config": []
                            },
                            {
                            "subsystem": "nvmf",
                            "config": []
                            },
                            {
                            "subsystem": "nbd",
                            "config": []
                            },
                            {
                            "subsystem": "scsi",
                            "config": []
                            },
                            {
                            "subsystem": "iscsi",
                            "config": []
                            },
                            {
                            "subsystem": "vhost",
                            "config": []
                            }
                        ]
                    }

# iscsi target method
iscsi_set_method =  {
                    "method": "iscsi_set_options",
                    "params": {
                        "node_base": "iqn.2016-06.io.spdk",
                        "max_sessions": 256,
                        "max_connections_per_session": 2,
                        "max_queue_depth": 256,
                        "default_time2wait": 2,
                        "default_time2retain": 20,
                        "first_burst_length": 65536,
                        "immediate_data": True,
                        "allow_duplicated_isid": False,
                        "error_recovery_level": 2,
                        "nop_timeout": 60,
                        "nop_in_interval": 30,
                        "disable_chap": False,
                        "require_chap": False,
                        "mutual_chap": False,
                        "chap_group": 0,
                        "max_large_datain_per_connection": 64,
                        "max_r2t_per_connection": 256,
                        "pdu_pool_size": 107520,
                        "immediate_data_pool_size": 8192,
                        "data_out_pool_size": 1024
                    }
                }

pg_method =     {
                "method": "iscsi_create_portal_group",
                "params": {
                    "tag": 1,
                    "portals": [
                        {
                            "host": "0.0.0.0",
                            "port": ""
                        }
                    ],
                    "private": False,
                    "wait": True
                }
            }

pg_start_method =   {
                    "method": "iscsi_start_portal_group",
                    "params": {
                        "tag": 1
                    }
                }

ig_method =     {
                "method": "iscsi_create_initiator_group",
                "params": {
                    "tag": "",
                    "initiators": [],
                    "netmasks": ["ANY"]
                }
            }

target_method = {
                "method": "iscsi_create_target_node",
                "params": {
                    "name": "",
                    "alias_name": "",
                    "pg_ig_maps": [
                    {
                        "pg_tag": 1,
                        "ig_tag": ""
                    }
                    ],
                    "luns": [],
                    "queue_depth": 256,
                    "disable_chap": False,
                    "require_chap": False,
                    "mutual_chap": False,
                    "chap_group": 0,
                    "header_digest": False,
                    "data_digest": False
                }
            }

redirect_method =   {
                    "method": "iscsi_target_node_set_redirect",
                    "params": {
                        "name": "target_iqn",
                        "pg_tag": 1,
                        "redirect_host": "",
                        "redirect_port": ""
                    }
                }

client_lun_method = {
                    "method": "iscsi_client_node_bind_lun",
                    "params": {
                        "name": "",
                        "bdev_name": "",
                        "iname": "",
                        "lun_map_id": ""
                    }
                }

ag_method =     {
                "method": "iscsi_create_auth_group",
                "params": {
                    "tag": "",
                    "secrets": []
                }
            }

# bdev method
iscsi_bdev_method = {
                    "method": "bdev_iscsi_create",
                    "params": {
                        "name": "",
                        "initiator_iqn": "",
                        "url": "",
                        "background": True
                    }
                }

aio_bdev_method =   {
                    "method": "bdev_aio_create",
                    "params": {
                        "name": "",
                        "block_size": 512,
                        "filename": ""
                    }
                }

raid_bdev_method =  {
                    "method": "bdev_raid_create",
                    "params": {
                    "name": "",
                    "strip_size_kb": 256,
                    "raid_level": "",
                    "base_bdevs": [],
                    "bitmap_file": "",
                    "chunksize_kb": 8,
                    "read_only": False,
                    "uuid": ""
                    }
                }
bdev_nvme_raid_create = {
          "method": "bdev_raid_create",
          "params": {
            "name": "",
            "strip_size_kb": 1024,
            "raid_level": "raid0",
            "base_bdevs": [
              "",
              ""
            ],
            "read_only": False,
            "uuid": ""
          }
        }
set_bdev_method =   {
                    "method": "bdev_set_options",
                    "params": {
                        "bdev_io_pool_size": 65535,
                        "bdev_io_cache_size": 256,
                        "bdev_auto_examine": True
                    }
                }
set_bdev_qos_method = {
                    "method": "bdev_raid_set_qos_token_bucket",
                    "params": {
                        "iops_limit_global": -1,
                        "bw_limit_global": -1,
                        "iops_limit_thread": -1,
                        "bw_limit_thread": -1
                    }
                }
nvme_set_op_method =   {
                        "method": "bdev_nvme_set_options",
                        "params": {
                            "action_on_timeout": "none",
                            "timeout_us": 0,
                            "timeout_admin_us": 0,
                            "keep_alive_timeout_ms": 10000,
                            "transport_retry_count": 4,
                            "arbitration_burst": 0,
                            "low_priority_weight": 0,
                            "medium_priority_weight": 0,
                            "high_priority_weight": 0,
                            "nvme_adminq_poll_period_us": 10000,
                            "nvme_ioq_poll_period_us": 0,
                            "io_queue_requests": 0,
                            "delay_cmd_submit": True,
                            "bdev_retry_count": 3,
                            "transport_ack_timeout": 0,
                            "fast_io_fail_timeout_sec": 0
                        }
                    }

nvme_set_hotplug =  {
                    "method": "bdev_nvme_set_hotplug",
                    "params": {
                        "period_us": 100000,
                        "enable": False
                    }
                }

bdev_nvme_attach_controller = {
          "method": "bdev_nvme_attach_controller",
          "params": {
            "name": "",
            "trtype": "TCP",
            "adrfam": "IPv4",
            "traddr": "",
            "trsvcid": "4421",
            "subnqn": "",
            "prchk_reftag": False,
            "prchk_guard": False,
            "fast_io_fail_timeout_sec": 0
          }
        }

# default methods for nvme subsystem
nvmf_set_config = {
          "method": "nvmf_set_config",
          "params": {
            "discovery_filter": "match_any",
            "admin_cmd_passthru": {
              "identify_ctrlr": False
            }
          }
        }

nvmf_set_max_subsystems = {
          "method": "nvmf_set_max_subsystems",
          "params": {
            "max_subsystems": 1024
          }
        }
nvmf_set_crdt = {
          "method": "nvmf_set_crdt",
          "params": {
            "crdt1": 0,
            "crdt2": 0,
            "crdt3": 0
          }
        }
nvmf_create_transport = {
          "method": "nvmf_create_transport",
          "params": {
            "trtype": "TCP",
            "max_queue_depth": 128,
            "max_io_qpairs_per_ctrlr": 8,
            "in_capsule_data_size": 8192,
            "max_io_size": 131072,
            "io_unit_size": 16384,
            "max_aq_depth": 128,
            "num_shared_buffers": 511,
            "buf_cache_size": 32,
            "dif_insert_or_strip": False,
            "zcopy": False,
            "c2h_success": True,
            "sock_priority": 0,
            "abort_timeout_sec": 1
          }
        }

nvmf_create_subsystem = {
          "method": "nvmf_create_subsystem",
          "params": {
            "nqn": "",
            "allow_any_host": True,
            "serial_number": "00000000000000000000",
            "model_number": "SPDK bdev Controller",
            "max_namespaces": 32,
            "min_cntlid": 1,
            "max_cntlid": 65519,
            "ana_reporting": False
          }
        }
nvmf_subsystem_add_listener = {
          "method": "nvmf_subsystem_add_listener",
          "params": {
            "nqn": "",
            "listen_address": {
              "trtype": "",
              "adrfam": "IPv4",
              "traddr": "",
              "trsvcid": ""
            }
          }
        }
nvmf_subsystem_add_host = {
          "method": "nvmf_subsystem_add_host",
          "params": {
            "nqn": "",
            "host": "nqn.2016-06.io.spdk:client"
          }
        }
nvmf_subsystem_add_ns = {
          "method": "nvmf_subsystem_add_ns",
          "params": {
            "nqn": "",
            "namespace": {
              "nsid": 1,
              "bdev_name": ""
            }
          }
        }

class Config_load(object):
    def __init__(self, args):
        self.error = False
        self.error_msg = ""
        self.vip_addr = args.vip
        list_addr = self.vip_addr.split(":")
        if len(list_addr) > 1:
            self.vip_addr = list_addr[0]
            self.vip_port = list_addr[1]
        else:
            self.vip_port = 5000
        if args.nodeip is not None:
            self.local_ip = args.nodeip
        else:
            self.local_ip = this_ip()
        if self.vip_addr == None:
            self.error = True
            self.error_msg = "vip addr is None!"
            return
        self.config = self.get_config()
        self.iscsi_port = self.get_iscsi_port()
        self.this_ip = self.local_ip + ':' + str(self.iscsi_port)
        self.this_ip_rd0 = self.local_ip + ':' + str(self.iscsi_port + 1159)
        return

    def get_config(self):
        """
        use the /config api to return the current gateway configuration
        :return: (dict) of the config object
        """
        if opt.role != "rd0":
            api_rqst = "http://{}:{}/api/config".format(self.vip_addr,self.vip_port)
        else:
            api_rqst = "http://{}:{}/api/diskTarget/{}".format(self.vip_addr,self.vip_port, self.local_ip)
        print("INFO: Request " + api_rqst)
        api = APIRequest(api_rqst)
        api.get()

        if api.response.status_code == 200:
            try:
                return api.response.json()
            except Exception:
                self.error = True
                self.error_msg = "get config from vip failed"
                pass
        else:
            raise Exception("request err", api.response.status_code, api.response.content)
        return {}
    
    def get_iscsi_port(self):
        if opt.role == 'redirect':
            return 3260
        else:
            return int(opt.port)
        

class APIRequest(object):
    def __init__(self, *args, **kwargs):
        self.args = args
        self.kwargs = kwargs

        # Establish defaults for the API connection
        if 'auth' not in self.kwargs:
            self.kwargs['auth'] = ('admin',
                                   'password')

        if 'timeout' not in self.kwargs:
            self.kwargs['timeout'] = 5

        self.http_methods = ['get', 'put', 'delete']
        self.data = None

    def _get_response(self):
        return self.data

    def __getattr__(self, name):
        if name in self.http_methods:
            request_method = getattr(requests, name)
            try:
                self.data = request_method(*self.args, **self.kwargs)
            except requests.ConnectionError:
                msg = ("Unable to connect to api endpoint @ "
                       "{}".format(self.args[0]))
                self.data = Response()
                self.data.status_code = 500
                self.data._content = '{{"message": "{}" }}'.format(msg).encode('utf-8')
                return self._get_response
            except requests.ReadTimeout:
                msg = ("ReadTimeout to api endpoint @ "
                       "{}".format(self.args[0]))
                self.data = Response()
                self.data.status_code = 500
                self.data._content = '{{"message": "{}" }}'.format(msg).encode('utf-8')
                return self._get_response
            except Exception:
                msg = ("Unknown error connecting to @ "
                       "{}".format(self.args[0]))
                self.data = Response()
                self.data.status_code = 500
                self.data._content = '{{"message": "{}" }}'.format(msg).encode('utf-8')
                return self._get_response
            else:
                # since the attribute is a callable, we must return with
                # a callable
                return self._get_response
        raise AttributeError()

    response = property(_get_response,
                        doc="get http response output")

class Parser(object):
    def __init__(self, opt, raw_config):
        self.error = False
        self.error_msg = ""
        self.config = raw_config
        self.tgt_config = self.parser_config(opt, config)

    def parser_config(self, opt, config):
        disks = config.config['disks']
        config_targets = config.config['targets']
        if opt.role == 'redirect':
            rc, out = self.parser_redirect(config_targets)
        elif opt.role == 'rd0':
            rc, out = self.parser_rd0(config_targets,disks)
        else:
            rc, out = self.parser_rd1(disks)
        if rc != 0:
            self.error = True
            self.error_msg = out
        return out

    def parser_redirect(self, iscsi_targets):
        ig_tag = 1
        iscsi_config = []
        bdev_config = []

        iscsi_config.append(iscsi_set_method)
        pg_attr = copy.deepcopy(pg_method)
        pg_attr['params']['portals'][0]['port'] = self.config.iscsi_port
        iscsi_config.append(pg_attr)

        for target_iqn, target_detail in iscsi_targets.items():
            iscsi_config = self.create_redirect_target(target_iqn, target_detail, ig_tag, iscsi_config)
            ig_tag += 1

        iscsi_config.append(pg_start_method)
        # makeup
        global_config = self.make_up(bdev_config, iscsi_config)
        return 0, global_config

    def disk_has_target(self, disk_name, targets):
        # check if disk has target
        image_name = disk_name
        parts = disk_name.split(".")
        if len(parts) >= 2:
            image_name = parts[1]
        target_name = "nqn.2016-06.io.spdk:bdev-" + image_name
        return target_name in targets

    def parser_rd0(self, targets, disks):
        ig_tag = 1
        ag_tag = 1
        iscsi_config = []
        nvmf_config = []
        bdev_config = []

        bdev_config.append(set_bdev_method)
        # bdev_config.append(set_bdev_qos_method)
        # bdev_nvme_set_options not permitted
        # bdev_config.append(nvme_set_op_method)

        has_iscsi_disk = False
        has_nvmf_disk = False
        # create disk
        for disk_name, disk_detail in disks.items():
            active_nodeaddrs = disk_detail['active_nodeip']
            for naddrs in active_nodeaddrs:
                active_nodeip = naddrs.split(":")[0]
                if self.config.local_ip == active_nodeip:
                    # check if disk has target
                    if not self.disk_has_target(disk_name, targets):
                        print("WARNING: disk {} has no target".format(disk_name))
                        continue
                    if disk_detail['protocol'] == "nvmf":
                        has_nvmf_disk = True
                        bdev_config = self.add_rd0_nvmf_bdev(disk_name, disk_detail, bdev_config)
                    else:
                        has_iscsi_disk = True
                        bdev_config = self.create_rd0_bdev(disk_name, disk_detail, bdev_config)

        if has_nvmf_disk:
            nvmf_config.append(nvmf_set_config)
            nvmf_config.append(nvmf_set_max_subsystems)
            nvmf_config.append(nvmf_set_crdt)
            # todo: use default transport tcp for now
            nvmf_config.append(nvmf_create_transport)
        if has_iscsi_disk:
            iscsi_config.append(iscsi_set_method)
            pg_attr = copy.deepcopy(pg_method)
            pg_attr['params']['portals'][0]['port'] = config.iscsi_port
            iscsi_config.append(pg_attr)
        # create target
        for target_qn, target_detail in targets.items():
            portals = target_detail['portals']
            for portal_addr in portals:
                portal_ip = portal_addr.split(":")[0]
                if self.config.local_ip == portal_ip:
                    if target_qn.startswith("nqn."):
                        # nvmf target
                        nvmf_config = self.add_rd0_nvmf_target(target_qn, target_detail, nvmf_config, bdev_config)
                    else:
                        # iscsi target
                        iscsi_config = self.create_rd0_target(target_qn, target_detail, ig_tag, ag_tag, iscsi_config)
                        ig_tag += 1
                        ag_tag += 1
        if has_iscsi_disk:
            iscsi_config.append(pg_start_method)
        bdev_config.append({"method": "bdev_wait_for_examine"})
        # makeup
        global_config = self.make_up(bdev_config, iscsi_config, nvmf_config)
        return 0, global_config

    def parser_rd1(self, disks):
        iscsi_config = []
        nvmf_config = []
        bdev_config = []

        bdev_config.append(set_bdev_method)
        # bdev_config.append(set_bdev_qos_method)
        # bdev_nvme_set_options not permitted
        # bdev_config.append(nvme_set_op_method)
        bdev_config.append(nvme_set_hotplug)

        pg_attr = copy.deepcopy(pg_method)
        pg_attr['params']['portals'][0]['port'] = config.iscsi_port
        iscsi_config.append(iscsi_set_method)
        iscsi_config.append(pg_attr)

        nvmf_config.append(nvmf_set_config)
        nvmf_config.append(nvmf_set_max_subsystems)
        nvmf_config.append(nvmf_set_crdt)
        # todo: use default transport tcp for now
        nvmf_config.append(nvmf_create_transport)

        # create disk
        for disk_name, disk_detail in disks.items():
            if config.this_ip in disk_detail['iplist']:
                bdev_config, iscsi_config, nvmf_config = self.create_rd1_bdev(disk_name, disk_detail, bdev_config, iscsi_config, nvmf_config)

        iscsi_config.append(pg_start_method)
        bdev_config.append({"method": "bdev_wait_for_examine"})
        # makeup
        global_config = self.make_up(bdev_config, iscsi_config, nvmf_config)
        return 0, global_config
    
    def create_redirect_target(self, target_iqn, target_detail, ig_tag, iscsi_config):
        ig_attr = copy.deepcopy(ig_method)
        target_attr = copy.deepcopy(target_method)

        for client_iqn in target_detail['client']:            
            ig_attr['params']['initiators'].append(client_iqn)
        ig_attr['params']['tag'] = ig_tag
        iscsi_config.append(ig_attr)

        target_attr['params']['name'] = target_iqn
        target_attr['params']['alias_name'] = target_iqn
        target_attr['params']['pg_ig_maps'][0]['ig_tag'] = ig_tag
        iscsi_config.append(target_attr)

        if target_detail['redirect_ip'] != None:
            ip, port = target_detail['redirect_ip'].split(':')
            redirect_attr = copy.deepcopy(redirect_method)
            redirect_attr['params']['name'] = target_iqn
            redirect_attr['params']['redirect_host'] = ip
            redirect_attr['params']['redirect_port'] = port
            iscsi_config.append(redirect_attr)

        return iscsi_config

    def create_rd0_bdev(self, disk_name, disk_detail, bdev_config):
        raid_bdev_attr = copy.deepcopy(raid_bdev_method)
        for info in disk_detail['slice_info']:
            iscsi_bdev_attr = copy.deepcopy(iscsi_bdev_method)
            bdev_name = disk_name + info['_part_index']
            iscsi_bdev_attr['params']['name'] = bdev_name + '_raid_0'
            iscsi_bdev_attr['params']['initiator_iqn'] = g_clientiqn + bdev_name + '_raid'
            #to do 需要转换port
            url = "iscsi://" + info['active_nodeip'] + '%' + info['active_nodeip'] + '@' + info['part_iplist'][0] + '/' + g_targetiqn + bdev_name + '_raid' + '/0'
            iscsi_bdev_attr['params']['url'] = url
            bdev_config.append(iscsi_bdev_attr)
            raid_bdev_attr['params']['base_bdevs'].append(bdev_name + '_raid_0')

        raid_bdev_attr['params']['name'] = disk_name + '_xstor'
        raid_bdev_attr['params']['raid_level'] = 'raid0'
        raid_bdev_attr['params']['uuid'] = disk_detail['uuid']
        bdev_config.append(raid_bdev_attr)

        return bdev_config

    def add_rd0_nvmf_bdev(self, disk_name, disk_detail, bdev_config):
        base_bdevs = []
        for slice in disk_detail["slice_info"]:
            bdev_attach_ctrl = copy.deepcopy(bdev_nvme_attach_controller)
            name = "{}_{}_raid_0".format(disk_name, slice["part_index"])
            subnqn = "{}{}-{}-raid".format(g_targetnqn, disk_name, slice["part_index"])
            subnqn = subnqn.replace("_", "-")
            bdev_attach_ctrl["params"]["name"] = name
            bdev_attach_ctrl["params"]["subnqn"] = subnqn
            addr = slice["part_iplist"][0]
            tr_parts = addr.split(":")
            bdev_attach_ctrl["params"]["traddr"] = tr_parts[0]
            bdev_attach_ctrl["params"]["trsvcid"] = tr_parts[1]
            # todo: use default transport type "TCP" for now
            bdev_attach_ctrl["params"]["trtype"] = "TCP"
            bdev_config.append(bdev_attach_ctrl)
            bdev_name = "{}_{}_raid_0n1".format(disk_name, slice["part_index"])
            base_bdevs.append(bdev_name)
        tmp_nvme_set_hotplug = copy.deepcopy(nvme_set_hotplug)
        bdev_config.append(tmp_nvme_set_hotplug)
        bdev_raid_create = copy.deepcopy(bdev_nvme_raid_create)
        raid_name = "{}_xstor".format(disk_name)
        bdev_raid_create["params"]["name"] = raid_name
        bdev_raid_create["params"]["raid_level"] = "raid0"
        bdev_raid_create["params"]["base_bdevs"] = base_bdevs
        bdev_raid_create["params"]["uuid"] = disk_detail["uuid"]
        bdev_raid_create["params"]["read_only"] = disk_detail["read_only"]
        bdev_config.append(bdev_raid_create)
        return bdev_config

    def create_rd0_target(self, target_iqn, target_detail, ig_tag, ag_tag, iscsi_config):
        ig_attr = copy.deepcopy(ig_method)
        ag_attr = copy.deepcopy(ag_method)
        target_attr = copy.deepcopy(target_method)

        for client_iqn in target_detail['client']:            
            ig_attr['params']['initiators'].append(client_iqn)
            if target_detail['client'][client_iqn]['auth']['auth']:
                user, secret = target_detail['client'][client_iqn]['auth']['auth'].split('/')
                secrets_attr = {'user': user, 'secret':secret, 'initiator': client_iqn}
                ag_attr['params']['secrets'].append(secrets_attr)
        ig_attr['params']['tag'] = ig_tag
        ag_attr['params']['tag'] = ag_tag
        iscsi_config.append(ig_attr)
        iscsi_config.append(ag_attr)

        target_attr['params']['name'] = target_iqn
        target_attr['params']['alias_name'] = target_iqn
        target_attr['params']['pg_ig_maps'][0]['ig_tag'] = ig_tag
        target_attr['params']['chap_group'] = ag_tag
        lun_id = 0
        for lun in target_detail['lun']:
            lun_attr = {'bdev_name': lun + '_xstor', 'lun_id': lun_id}
            target_attr['params']['luns'].append(lun_attr)
            lun_id += 1
        iscsi_config.append(target_attr)

        for client_iqn, client_detail in target_detail['client'].items():
            lun_id = 0
            for lun in client_detail['luns']:
                clientlun_attr = copy.deepcopy(client_lun_method)
                clientlun_attr['params']['name'] = target_iqn
                clientlun_attr['params']['bdev_name'] = lun + '_xstor'
                clientlun_attr['params']['iname'] = client_iqn
                clientlun_attr['params']['lun_map_id'] = lun_id
                lun_id += 1
                iscsi_config.append(clientlun_attr)

        return iscsi_config
    
    def find_bdev_info(self, image_name, bdev_config):
        for bconfig in bdev_config:
            if bconfig["method"] == "bdev_raid_create":
                tmp_raid_name = bconfig["params"]["name"]
                tmp_image_name = tmp_raid_name.split(".")[1].replace("_xstor", "")
                if tmp_image_name == image_name:
                    return bconfig["params"]

    def add_rd0_nvmf_target(self, target_qn, target_detail, nvmf_config, bdev_config):
        if target_detail['client'] == None:
            print("WARNING: target " + target_qn + " has no client")
            return nvmf_config
        image_name = target_qn.replace("nqn.2016-06.io.spdk:bdev-", "")
        bdev_info = self.find_bdev_info(image_name, bdev_config)
        if bdev_info == None:
            print("WARNING: target bdev for image: " + image_name + " is not found, check if disk is created.")
            return nvmf_config
        create_subsystem = copy.deepcopy(nvmf_create_subsystem)
        subsystem_add_listener = copy.deepcopy(nvmf_subsystem_add_listener)
        subsystem_add_host = copy.deepcopy(nvmf_subsystem_add_host)
        # subsystem_add_host_as_rd1client use as client host to connect to rd1 tgt
        subsystem_add_host_as_rd1client = copy.deepcopy(nvmf_subsystem_add_host)
        subsystem_add_ns = copy.deepcopy(nvmf_subsystem_add_ns)

        create_subsystem["params"]["nqn"] = target_qn
        subsystem_add_listener["params"]["nqn"]= target_qn
        subsystem_add_listener["params"]["listen_address"]["traddr"]= self.config.local_ip
        # todo: use default trtype TCP
        subsystem_add_listener["params"]["listen_address"]["trtype"]= "TCP"
        subsystem_add_listener["params"]["listen_address"]["trsvcid"]= target_detail.get("listen_port", "4420")
        for client_nqn in target_detail['client']:
            subsystem_add_host["params"]["nqn"] = target_qn
            subsystem_add_host["params"]["host"] = client_nqn
        subsystem_add_host_as_rd1client["params"]["nqn"] = target_qn

        subsystem_add_ns["params"]["nqn"] = target_qn
        subsystem_add_ns["params"]["namespace"]["bdev_name"] = bdev_info["name"]
        subsystem_add_ns["params"]["namespace"]["nsid"] = 1
        # no need to specific nguid and uuid, spdk will find the right one.
        # subsystem_add_ns["params"]["namespace"]["nguid"] = bdev_info["uuid"].replace("-", "").upper()
        # subsystem_add_ns["params"]["namespace"]["uuid"] = bdev_info["uuid"]

        nvmf_config.append(create_subsystem)
        nvmf_config.append(subsystem_add_listener)
        nvmf_config.append(subsystem_add_host)
        nvmf_config.append(subsystem_add_host_as_rd1client)
        nvmf_config.append(subsystem_add_ns)
        return nvmf_config

    def create_rd1_bdev(self, disk_name, disk_detail, bdev_config, iscsi_config, nvmf_config):
        ig_tag = 1
        ag_tag = 1
        for info in disk_detail['slice_info']:
            if config.this_ip_rd0 in info['part_iplist']:
                aio_bdev_attr = copy.deepcopy(aio_bdev_method)
                iscsi_bdev_attr = copy.deepcopy(iscsi_bdev_method)
                raid_bdev_attr = copy.deepcopy(raid_bdev_method)
                pool_name = disk_name.split('.')[0]

                remote_ip = self.get_remoteip(info['part_iplist'])
                bdev_name = disk_name + '_' + info['part_index']
                bdev_qn_name = bdev_name.replace("_", "-")
                zvol_name = bdev_name.replace('.', '/')
                aio_bdev_attr['params']['name'] = bdev_name
                aio_bdev_attr['params']['filename'] = "/dev/zvol/" + pool_name + '_' + str(config.iscsi_port) + '/' + bdev_name.split('.')[-1]
                bdev_config.append(aio_bdev_attr)

                iscsi_bdev_attr['params']['name'] = bdev_name + '_0'
                iscsi_bdev_attr['params']['initiator_iqn'] = g_clientiqn + bdev_qn_name
                url = "iscsi://" + config.this_ip + '%' + config.this_ip + '@' + remote_ip + '/' + g_targetiqn + bdev_qn_name + '/0'
                iscsi_bdev_attr['params']['url'] = url
                bdev_config.append(iscsi_bdev_attr)

                raid_bdev_attr['params']['base_bdevs'].append(bdev_name)
                raid_bdev_attr['params']['base_bdevs'].append(bdev_name + '_0')
                raid_bdev_attr['params']['name'] = bdev_name + '_raid'
                raid_bdev_attr['params']['raid_level'] = 'raid1'
                raid_bdev_attr['params']['uuid'] = info['raid1_uuid']
                raid_bdev_attr['params']['bitmap_file'] = "/opt/bitmap_meta/" + pool_name + '_' + str(config.iscsi_port) + '/' + bdev_name + '_raid.bm',
                bdev_config.append(raid_bdev_attr)

                # create rd1 to rd1 private target
                iscsi_config = self.create_rd1_to_rd1_target(bdev_name, ig_tag, ag_tag, remote_ip, iscsi_config)
                ig_tag += 1
                ag_tag += 1

                # create rd1 to rd0 pri-target
                if disk_detail['bondTord0'] and config.this_ip_rd0 == info['part_iplist'][0]:
                    nvmf_config = self.create_rd1_to_rd0_target(bdev_name, info, nvmf_config)

        return bdev_config, iscsi_config, nvmf_config
    
    def create_rd1_to_rd1_target(self, bdev_name, ig_tag, ag_tag, remote_ip, iscsi_config):
        ig_attr = copy.deepcopy(ig_method)
        ag_attr = copy.deepcopy(ag_method)
        target_attr = copy.deepcopy(target_method)
        clientlun_attr = copy.deepcopy(client_lun_method)
        client_iqn = g_clientiqn + bdev_name
        target_iqn = g_targetiqn + bdev_name
        client_iqn = client_iqn.replace("_", "-")
        target_iqn = target_iqn.replace("_", "-")

        ig_attr['params']['initiators'].append(client_iqn)
        secrets_attr = {'user': remote_ip, 'secret':remote_ip, 'initiator': client_iqn}
        ag_attr['params']['secrets'].append(secrets_attr)
        ig_attr['params']['tag'] = ig_tag
        ag_attr['params']['tag'] = ag_tag
        iscsi_config.append(ig_attr)
        iscsi_config.append(ag_attr)

        target_attr['params']['name'] = target_iqn
        target_attr['params']['alias_name'] = target_iqn
        target_attr['params']['pg_ig_maps'][0]['ig_tag'] = ig_tag
        target_attr['params']['chap_group'] = ag_tag

        lun_attr = {'bdev_name': bdev_name, 'lun_id': 0}
        target_attr['params']['luns'].append(lun_attr)
        iscsi_config.append(target_attr)

        clientlun_attr['params']['name'] = target_iqn
        clientlun_attr['params']['bdev_name'] = bdev_name
        clientlun_attr['params']['iname'] = client_iqn
        clientlun_attr['params']['lun_map_id'] = 0
        iscsi_config.append(clientlun_attr)

        return iscsi_config

    def create_rd1_to_rd0_target(self,bdev_name, info, nvmf_config):
        create_subsystem = copy.deepcopy(nvmf_create_subsystem)
        subsystem_add_listener = copy.deepcopy(nvmf_subsystem_add_listener)
        subsystem_add_host = copy.deepcopy(nvmf_subsystem_add_host)
        # subsystem_add_host_as_rd1client use as client host to connect to rd1 tgt
        subsystem_add_host_as_rd1client = copy.deepcopy(nvmf_subsystem_add_host)
        subsystem_add_ns = copy.deepcopy(nvmf_subsystem_add_ns)

        bdev_qn_name = bdev_name.replace("_", "-")
        target_nqn = f'{g_targetnqn}{bdev_qn_name}-raid'
        client_nqn = f'{g_clientnqn}{bdev_qn_name}-raid'
        create_subsystem["params"]["nqn"] = target_nqn
        subsystem_add_listener["params"]["nqn"]= target_nqn
        subsystem_add_listener["params"]["listen_address"]["traddr"]= self.config.local_ip
        # todo: use default trtype TCP
        subsystem_add_listener["params"]["listen_address"]["trtype"]= "TCP"
        subsystem_add_listener["params"]["listen_address"]["trsvcid"]= "4421"
        subsystem_add_host["params"]["nqn"] = target_nqn
        subsystem_add_host["params"]["host"] = client_nqn
        subsystem_add_host_as_rd1client["params"]["nqn"] = target_nqn

        subsystem_add_ns["params"]["nqn"] = target_nqn
        subsystem_add_ns["params"]["namespace"]["bdev_name"] = f"{bdev_name}_raid"
        subsystem_add_ns["params"]["namespace"]["nsid"] = 1
        subsystem_add_ns["params"]["namespace"]["nguid"] = info['raid1_uuid'].replace("-", "").upper()
        subsystem_add_ns["params"]["namespace"]["uuid"] = info['raid1_uuid']

        nvmf_config.append(create_subsystem)
        nvmf_config.append(subsystem_add_listener)
        nvmf_config.append(subsystem_add_host)
        nvmf_config.append(subsystem_add_host_as_rd1client)
        nvmf_config.append(subsystem_add_ns)
        return nvmf_config        

    def get_remoteip(self, iplist):
        if iplist[0] == config.this_ip_rd0:
            return iplist[1].split(':')[0] + ':' + str(config.iscsi_port)
        else:
            return iplist[0].split(':')[0] + ':' + str(config.iscsi_port)
        
    def make_up(self, bdev_config, iscsi_config, nvme_config=[]):
        global_config = copy.deepcopy(global_config_seed)
        for subsystem in global_config['subsystems']:
            if subsystem['subsystem'] == 'bdev':
                subsystem['config'] = bdev_config
            if subsystem['subsystem'] == 'iscsi':
                subsystem['config'] = iscsi_config
            if subsystem['subsystem'] == 'nvmf':
                subsystem['config'] = nvme_config
        return global_config

def save_config(tgt_config, config_file):
    filename = config_file
    if filename is None or filename == "":
        if opt.role == 'redirect':
            dir = redirect_dir
        else:
            dir = f'{tgt_dir}{opt.port}/'
        file_lists = os.listdir(dir)
        file_lists.sort(key=lambda fn: os.path.getmtime(dir + "/" + fn)
                        if not os.path.isdir(dir + "/" + fn) else 0)
        if len(file_lists) > 100:
            filename = os.path.join(dir, file_lists[0])
            os.remove(filename)
        filename = dir + 'globle_config_' + datetime.datetime.now().strftime('%Y-%m-%d-%H-%M-%S,%f')[:-3] + '.json'
    try:
        with open(filename,"w") as f:
            json.dump(copy.deepcopy(tgt_config), f, indent=2)
        f.close()
        print("config create is ok:{}".format(filename))
    except Exception:
        os.remove(filename)
        halt("save tgt config file failed")
    return

def this_ip():
    """
    return the local machine's ip
    """
    return socket.gethostbyname(socket.getfqdn())

def halt(message):
    print(message)
    sys.exit(1)

def main(opt, config):
    parser_out = Parser(opt, config)
    if parser_out.error:
        halt(parser_out.error_msg)
    save_config(parser_out.tgt_config, opt.output)
    sys.exit(0)

if __name__ == "__main__":
    parse = argparse.ArgumentParser("setup")
    parse.add_argument('-r', '--role', type=str, help='The role of tgt')
    parse.add_argument('-p', '--port', type=str, default="3261", help='The port of (rd0|rd1')
    parse.add_argument('-v', '--vip', type=str, help='The addr of vip')
    parse.add_argument('-a', '--nodeip', type=str, help='The addr of tgt(rd0|rd1) node')
    parse.add_argument('-o', '--output', type=str, help='Output file for parse result')
    
    opt = parse.parse_args()

    if opt.role not in ['redirect','rd1','rd0']:
        halt("Input wrong role, role must be one of 'redirect','rd1','rd0'! ")


    if opt.vip == None:
        halt("The vip can not be None! ")

    if opt.role == 'redirect' and opt.port == None:
        halt("The port can not be None, when role is rd1! ")

    args = opt
    err_msg = None
    for addr in opt.vip.split(','):
        err_msg = None
        args.vip = addr
        try:
            config = Config_load(args)
            break
        except Exception as e:
            print(e)
            err_msg = str(e)
            continue
    if err_msg:
        halt(err_msg)
    else:
        main(opt, config)
