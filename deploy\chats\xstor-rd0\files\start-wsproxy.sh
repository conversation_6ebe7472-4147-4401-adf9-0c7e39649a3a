#!/bin/bash

set -x
export TZ=Asia/Shanghai
source /root/utils
start_wsproxy() {
    ensure_env_var NodeHostIP
    ensure_env_var TAPIPort
    ensure_env_var MGRAddr
    log_exec /root/wsproxy -m proxy -s ${MGRAddr} -t ${NodeHostIP}:${TAPIPort}
    if [ $? -ne 0 ]; then
        log_error "start_wsproxy failed."
        exit -1
    fi
}
main() {
    if [ ! -z "$1" ] && [ "$1" == "rbd" ]; then
        log_info "No need to use proxy for rbd, tail hanging..."
        log_exec tail -f /dev/null
    elif [ "$1" == "light" ]; then
        log_info "Starting wsproxy..."
        start_wsproxy
    fi
}
main "$@"